# Ebook Indexer

A comprehensive ebook directory analysis and indexing tool with anomaly detection, built with MongoDB and Python.

## Project Status

### ✅ Phase 1: Foundation & Core Infrastructure (COMPLETED)

- [x] Project setup with uv and pyproject.toml
- [x] Directory structure created
- [x] Configuration management system (`config/settings.py`)
  - YAML configuration file support
  - Environment variable overrides
  - Default configuration with sensible defaults
- [x] Custom exception classes (`exceptions/custom_exceptions.py`)
- [x] Logging configuration (`utils/logging_config.py`)
  - Console and file logging
  - Log rotation support
  - Logger mixin for easy integration
- [x] Comprehensive test suite (65 tests passing)
- [x] Demo script showing foundation components

### ✅ Phase 2: Data Models & Database Foundation (COMPLETED)

- [x] Pydantic models for MongoDB documents
  - BookDocument, ProcessingJob, AnomalyReport models
  - Comprehensive validation and serialization
  - Enum types for status and categories
- [x] MongoDB connection management
  - Connection pooling and health checks
  - Context manager support
  - Global connection management
- [x] Basic repository pattern implementation
  - BookRepository, JobRepository, AnomalyRepository
  - CRUD operations with ObjectId handling
  - Query methods for complex operations
- [x] Database indexes configuration
  - Performance-optimized indexes
  - Compound indexes for complex queries

### 🚧 Next: Phase 3: Core Business Logic - Simple Version

- [ ] File utilities (hashing, size calculation)
- [ ] Basic directory scanner (without anomaly detection)
- [ ] Metadata extractor (PDF support first)
- [ ] Integration tests with actual MongoDB

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd ebook-indexer

# Install dependencies using uv
uv sync --extra dev
```

## Usage

### Running Tests

```bash
# Run all tests
uv run pytest ebook_indexer/tests/ -v

# Run specific test file
uv run pytest ebook_indexer/tests/test_config.py -v
```

### Demo

```bash
# Run foundation demo
uv run python demo_foundation.py

# Run database demo
uv run python demo_database.py
```

### Configuration

Create a `config.yaml` file (see `config.yaml.example` for reference):

```yaml
mongodb:
  url: "mongodb://localhost:27017"
  database: "ebook_indexer"

root_directories:
  - "/path/to/your/ebooks"

processing:
  max_workers: 4
  batch_size: 100

logging:
  level: "INFO"
  file: "ebook_indexer.log"
```

## Architecture

The project follows a modular architecture with clear separation of concerns:

- **config/**: Configuration management
- **core/**: Business logic (scanner, indexer, anomaly detection)
- **database/**: Data models and repository pattern
- **utils/**: Utility functions and helpers
- **exceptions/**: Custom exception classes
- **tests/**: Comprehensive test suite

## Development

### Code Quality

```bash
# Format code
uv run black ebook_indexer/

# Lint code
uv run flake8 ebook_indexer/

# Run tests
uv run pytest
```

### Project Structure

```
ebook_indexer/
├── config/
│   ├── __init__.py
│   └── settings.py             # ✅ Configuration management
├── core/
│   ├── __init__.py
│   ├── scanner.py              # 🚧 Directory scanning
│   ├── metadata_extractor.py   # 🚧 Metadata extraction
│   ├── indexer.py              # 🚧 Main indexing orchestrator
│   └── anomaly_detector.py     # 🚧 Anomaly detection logic
├── database/
│   ├── __init__.py
│   ├── models.py               # ✅ Pydantic models
│   ├── repository.py           # ✅ MongoDB data access
│   └── connection.py           # ✅ MongoDB connection
├── utils/
│   ├── __init__.py
│   ├── file_utils.py           # 🚧 File handling utilities
│   ├── logging_config.py       # ✅ Logging configuration
│   └── progress_tracker.py     # 🚧 Progress tracking
├── exceptions/
│   ├── __init__.py
│   └── custom_exceptions.py    # ✅ Custom exception classes
└── tests/
    ├── __init__.py
    ├── test_config.py          # ✅ Configuration tests
    ├── test_logging.py         # ✅ Logging tests
    ├── test_models.py          # ✅ Database model tests
    ├── test_connection.py      # ✅ MongoDB connection tests
    ├── test_repository.py      # ✅ Repository pattern tests
    └── ...                     # 🚧 More tests to come
```

## Features (Planned)

- **Directory Scanning**: Recursive scanning of ebook directories
- **Metadata Extraction**: Extract metadata from PDF, EPUB, MOBI files
- **Anomaly Detection**: Detect misplaced files, naming violations, deep nesting
- **MongoDB Storage**: Flexible document storage with rich querying
- **CLI Interface**: Command-line interface for all operations
- **Progress Tracking**: Real-time progress reporting
- **Resume Capability**: Resume interrupted indexing jobs
- **Retry Logic**: Automatic retry of failed operations

## License

[Add your license here]
