"""Configuration management for ebook indexer."""

import os
import yaml
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from pathlib import Path


@dataclass
class AppConfig:
    """Main application configuration."""
    
    # MongoDB settings
    mongodb_url: str = "mongodb://localhost:27017"
    database_name: str = "ebook_indexer"
    
    # Processing settings
    root_directories: List[str] = field(default_factory=list)
    supported_extensions: List[str] = field(default_factory=list)
    max_workers: int = 4
    batch_size: int = 100
    retry_attempts: int = 3
    retry_delay: int = 60
    
    # Anomaly detection settings
    anomaly_detection: Dict[str, Any] = field(default_factory=dict)
    
    # Logging settings
    log_level: str = "INFO"
    log_file: Optional[str] = "ebook_indexer.log"
    log_rotation: str = "daily"
    
    def __post_init__(self):
        """Set default values for complex fields."""
        if not self.supported_extensions:
            self.supported_extensions = ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
        
        if not self.anomaly_detection:
            self.anomaly_detection = {
                'max_nesting_depth': 4,
                'enforce_naming_convention': True,
                'detect_misplaced_files': True,
                'severity_thresholds': {
                    'wrong_level': 'medium',
                    'deep_nesting': 'low',
                    'missing_directory': 'high',
                    'naming_violation': 'low'
                }
            }


def load_config_from_file(config_path: str) -> AppConfig:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    
    # Flatten nested structure for dataclass
    flattened = {}
    
    # MongoDB settings
    if 'mongodb' in config_data:
        flattened['mongodb_url'] = config_data['mongodb'].get('url', 'mongodb://localhost:27017')
        flattened['database_name'] = config_data['mongodb'].get('database', 'ebook_indexer')
    
    # Processing settings
    if 'processing' in config_data:
        proc = config_data['processing']
        flattened['max_workers'] = proc.get('max_workers', 4)
        flattened['batch_size'] = proc.get('batch_size', 100)
        flattened['retry_attempts'] = proc.get('retry_attempts', 3)
        flattened['retry_delay'] = proc.get('retry_delay', 60)
    
    # Direct mappings
    flattened['root_directories'] = config_data.get('root_directories', [])
    flattened['supported_extensions'] = config_data.get('supported_formats', [])
    flattened['anomaly_detection'] = config_data.get('anomaly_detection', {})
    
    # Logging settings
    if 'logging' in config_data:
        log = config_data['logging']
        flattened['log_level'] = log.get('level', 'INFO')
        flattened['log_file'] = log.get('file', 'ebook_indexer.log')
        flattened['log_rotation'] = log.get('rotation', 'daily')
    
    return AppConfig(**flattened)


def load_config_from_env() -> AppConfig:
    """Load configuration from environment variables."""
    config_data = {}
    
    # MongoDB settings
    if os.getenv('MONGODB_URL'):
        config_data['mongodb_url'] = os.getenv('MONGODB_URL')
    if os.getenv('DATABASE_NAME'):
        config_data['database_name'] = os.getenv('DATABASE_NAME')
    
    # Processing settings
    if os.getenv('MAX_WORKERS'):
        config_data['max_workers'] = int(os.getenv('MAX_WORKERS'))
    if os.getenv('BATCH_SIZE'):
        config_data['batch_size'] = int(os.getenv('BATCH_SIZE'))
    if os.getenv('RETRY_ATTEMPTS'):
        config_data['retry_attempts'] = int(os.getenv('RETRY_ATTEMPTS'))
    
    # Root directories from environment (comma-separated)
    if os.getenv('ROOT_DIRECTORIES'):
        config_data['root_directories'] = [
            d.strip() for d in os.getenv('ROOT_DIRECTORIES').split(',')
        ]
    
    # Logging
    if os.getenv('LOG_LEVEL'):
        config_data['log_level'] = os.getenv('LOG_LEVEL')
    if os.getenv('LOG_FILE'):
        config_data['log_file'] = os.getenv('LOG_FILE')
    
    return AppConfig(**config_data)


def get_config(config_file: Optional[str] = None) -> AppConfig:
    """
    Get configuration with precedence: file -> environment -> defaults.
    
    Args:
        config_file: Path to YAML configuration file
        
    Returns:
        AppConfig instance with merged configuration
    """
    # Start with defaults
    config = AppConfig()
    
    # Override with file config if provided
    if config_file and Path(config_file).exists():
        file_config = load_config_from_file(config_file)
        # Merge file config into default config
        for field_name in config.__dataclass_fields__:
            file_value = getattr(file_config, field_name)
            if file_value != getattr(AppConfig(), field_name):  # If not default value
                setattr(config, field_name, file_value)
    
    # Override with environment variables
    env_config = load_config_from_env()
    for field_name in config.__dataclass_fields__:
        env_value = getattr(env_config, field_name)
        if env_value != getattr(AppConfig(), field_name):  # If not default value
            setattr(config, field_name, env_value)
    
    return config
