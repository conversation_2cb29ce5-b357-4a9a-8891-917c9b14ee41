"""Tests for configuration management."""

import os
import tempfile
import pytest
from pathlib import Path

from ebook_indexer.config.settings import AppConfig, load_config_from_file, load_config_from_env, get_config


def test_default_config():
    """Test default configuration values."""
    config = AppConfig()
    
    assert config.mongodb_url == "mongodb://localhost:27017"
    assert config.database_name == "ebook_indexer"
    assert config.max_workers == 4
    assert config.batch_size == 100
    assert config.retry_attempts == 3
    assert config.supported_extensions == ['.pdf', '.epub', '.mobi', '.azw', '.azw3']
    assert config.anomaly_detection['max_nesting_depth'] == 4
    assert config.log_level == "INFO"


def test_config_from_yaml():
    """Test loading configuration from YAML file."""
    yaml_content = """
mongodb:
  url: "mongodb://test:27017"
  database: "test_db"

processing:
  max_workers: 8
  batch_size: 50

root_directories:
  - "/test/path1"
  - "/test/path2"

supported_formats:
  - ".pdf"
  - ".epub"

logging:
  level: "DEBUG"
  file: "test.log"
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(yaml_content)
        f.flush()
        
        try:
            config = load_config_from_file(f.name)
            
            assert config.mongodb_url == "mongodb://test:27017"
            assert config.database_name == "test_db"
            assert config.max_workers == 8
            assert config.batch_size == 50
            assert config.root_directories == ["/test/path1", "/test/path2"]
            assert config.supported_extensions == [".pdf", ".epub"]
            assert config.log_level == "DEBUG"
            assert config.log_file == "test.log"
            
        finally:
            os.unlink(f.name)


def test_config_from_env():
    """Test loading configuration from environment variables."""
    # Set environment variables
    env_vars = {
        'MONGODB_URL': 'mongodb://env:27017',
        'DATABASE_NAME': 'env_db',
        'MAX_WORKERS': '6',
        'BATCH_SIZE': '75',
        'ROOT_DIRECTORIES': '/env/path1,/env/path2',
        'LOG_LEVEL': 'WARNING'
    }
    
    # Save original values
    original_values = {}
    for key in env_vars:
        original_values[key] = os.environ.get(key)
        os.environ[key] = env_vars[key]
    
    try:
        config = load_config_from_env()
        
        assert config.mongodb_url == 'mongodb://env:27017'
        assert config.database_name == 'env_db'
        assert config.max_workers == 6
        assert config.batch_size == 75
        assert config.root_directories == ['/env/path1', '/env/path2']
        assert config.log_level == 'WARNING'
        
    finally:
        # Restore original values
        for key, value in original_values.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value


def test_config_file_not_found():
    """Test handling of missing configuration file."""
    with pytest.raises(FileNotFoundError):
        load_config_from_file("/nonexistent/config.yaml")


def test_get_config_precedence():
    """Test configuration precedence: file -> env -> defaults."""
    # Create a test config file
    yaml_content = """
mongodb:
  url: "mongodb://file:27017"
  database: "file_db"

processing:
  max_workers: 2
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(yaml_content)
        f.flush()
        
        # Set environment variable that should override file
        os.environ['MONGODB_URL'] = 'mongodb://env:27017'
        
        try:
            config = get_config(f.name)
            
            # Environment should override file
            assert config.mongodb_url == 'mongodb://env:27017'
            # File should override default
            assert config.database_name == 'file_db'
            assert config.max_workers == 2
            # Default should be used when not specified
            assert config.batch_size == 100
            
        finally:
            os.unlink(f.name)
            os.environ.pop('MONGODB_URL', None)
