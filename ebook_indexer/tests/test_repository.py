"""Tests for repository classes."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from bson import ObjectId

from ebook_indexer.database.repository import BookRepository, JobRepository, AnomalyRepository
from ebook_indexer.database.models import (
    BookDocument, ProcessingJob, AnomalyReport,
    FileInfo, StructureInfo, BookMetadata, ProcessingInfo,
    ProcessingStatus, JobStatus, AnomalyType, AnomalySeverity
)
from ebook_indexer.exceptions.custom_exceptions import DatabaseError, ValidationError


class TestBaseRepository:
    """Test base repository functionality."""

    @patch('ebook_indexer.database.repository.get_connection')
    def test_collection_property(self, mock_get_connection):
        """Test collection property initialization."""
        mock_connection = Mock()
        mock_database = Mock()
        mock_collection = Mock()
        mock_database.__getitem__ = Mock(return_value=mock_collection)
        mock_connection.get_database.return_value = mock_database
        mock_get_connection.return_value = mock_connection

        repo = BookRepository()
        collection = repo.collection

        assert collection == mock_collection
        mock_database.__getitem__.assert_called_once_with("books")


class TestBookRepository:
    """Test book repository."""

    def setup_method(self):
        """Set up test fixtures."""
        self.repo = BookRepository()
        self.mock_collection = Mock()
        self.repo._collection = self.mock_collection

        # Create test book document
        self.test_book = BookDocument(
            file_info=FileInfo(
                file_path="/test/book.pdf",
                directory_path="/test",
                filename="book.pdf",
                file_extension=".pdf",
                file_size=1024000,
                file_hash="abc123",
                last_modified=datetime.utcnow()
            ),
            metadata=BookMetadata(title="Test Book", author="Test Author"),
            structure_info=StructureInfo(
                expected_path="/test/book.pdf",
                collection_name="test-collection",
                book_directory="test-book",
                nesting_level=3,
                follows_convention=True
            ),
            processing=ProcessingInfo(
                status=ProcessingStatus.COMPLETED,
                job_id=str(ObjectId())
            )
        )

    def test_save_book_new(self):
        """Test saving new book."""
        # Mock insert result
        mock_result = Mock()
        mock_result.inserted_id = ObjectId()
        self.mock_collection.insert_one.return_value = mock_result

        # Remove ID to simulate new document
        self.test_book.id = None

        doc_id = self.repo.save_book(self.test_book)

        assert doc_id == str(mock_result.inserted_id)
        self.mock_collection.insert_one.assert_called_once()

    def test_save_book_update(self):
        """Test updating existing book."""
        # Mock update result
        mock_result = Mock()
        mock_result.matched_count = 1
        self.mock_collection.replace_one.return_value = mock_result

        # Set ID to simulate existing document
        test_id = ObjectId()
        self.test_book.id = test_id

        doc_id = self.repo.save_book(self.test_book)

        assert doc_id == str(test_id)
        self.mock_collection.replace_one.assert_called_once()

    def test_save_book_update_not_found(self):
        """Test updating book that doesn't exist."""
        # Mock update result with no matches
        mock_result = Mock()
        mock_result.matched_count = 0
        self.mock_collection.replace_one.return_value = mock_result

        # Set ID to simulate existing document
        self.test_book.id = ObjectId()

        with pytest.raises(DatabaseError) as exc_info:
            self.repo.save_book(self.test_book)

        assert "not found for update" in str(exc_info.value)

    def test_get_book_by_id_found(self):
        """Test getting book by ID when found."""
        test_id = ObjectId()
        mock_doc = self.test_book.model_dump(by_alias=True)
        mock_doc["_id"] = test_id
        self.mock_collection.find_one.return_value = mock_doc

        book = self.repo.get_book_by_id(str(test_id))

        assert book is not None
        assert isinstance(book, BookDocument)
        assert book.file_info.filename == "book.pdf"
        self.mock_collection.find_one.assert_called_once_with({"_id": test_id})

    def test_get_book_by_id_not_found(self):
        """Test getting book by ID when not found."""
        test_id = ObjectId()
        self.mock_collection.find_one.return_value = None

        book = self.repo.get_book_by_id(str(test_id))

        assert book is None
        self.mock_collection.find_one.assert_called_once_with({"_id": test_id})

    def test_get_book_by_path_found(self):
        """Test getting book by file path when found."""
        mock_doc = self.test_book.model_dump(by_alias=True)
        self.mock_collection.find_one.return_value = mock_doc

        book = self.repo.get_book_by_path("/test/book.pdf")

        assert book is not None
        assert isinstance(book, BookDocument)
        self.mock_collection.find_one.assert_called_once_with({
            "file_info.file_path": "/test/book.pdf"
        })

    def test_get_books_by_job(self):
        """Test getting books by job ID."""
        job_id = ObjectId()
        mock_docs = [self.test_book.model_dump(by_alias=True)]
        self.mock_collection.find.return_value = mock_docs

        books = self.repo.get_books_by_job(str(job_id))

        assert len(books) == 1
        assert isinstance(books[0], BookDocument)
        self.mock_collection.find.assert_called_once_with({
            "processing.job_id": job_id
        })

    def test_get_books_by_job_with_status(self):
        """Test getting books by job ID with status filter."""
        job_id = ObjectId()
        mock_docs = [self.test_book.model_dump(by_alias=True)]
        self.mock_collection.find.return_value = mock_docs

        books = self.repo.get_books_by_job(str(job_id), ProcessingStatus.COMPLETED)

        assert len(books) == 1
        self.mock_collection.find.assert_called_once_with({
            "processing.job_id": job_id,
            "processing.status": "completed"
        })

    def test_get_retry_candidates(self):
        """Test getting retry candidates."""
        mock_docs = [self.test_book.model_dump(by_alias=True)]
        self.mock_collection.find.return_value = mock_docs

        books = self.repo.get_retry_candidates()

        assert len(books) == 1
        # Verify the complex query structure
        call_args = self.mock_collection.find.call_args[0][0]
        assert "$or" in call_args
        assert len(call_args["$or"]) == 2

    def test_update_book_success(self):
        """Test updating book successfully."""
        mock_result = Mock()
        mock_result.matched_count = 1
        self.mock_collection.replace_one.return_value = mock_result

        self.test_book.id = ObjectId()
        result = self.repo.update_book(self.test_book)

        assert result is True
        self.mock_collection.replace_one.assert_called_once()

    def test_update_book_no_id(self):
        """Test updating book without ID."""
        self.test_book.id = None

        with pytest.raises(ValidationError) as exc_info:
            self.repo.update_book(self.test_book)

        assert "Book ID is required for update" in str(exc_info.value)

    def test_update_book_not_found(self):
        """Test updating book that doesn't exist."""
        mock_result = Mock()
        mock_result.matched_count = 0
        self.mock_collection.replace_one.return_value = mock_result

        self.test_book.id = ObjectId()
        result = self.repo.update_book(self.test_book)

        assert result is False

    def test_delete_book_success(self):
        """Test deleting book successfully."""
        mock_result = Mock()
        mock_result.deleted_count = 1
        self.mock_collection.delete_one.return_value = mock_result

        test_id = ObjectId()
        result = self.repo.delete_book(str(test_id))

        assert result is True
        self.mock_collection.delete_one.assert_called_once_with({"_id": test_id})

    def test_delete_book_not_found(self):
        """Test deleting book that doesn't exist."""
        mock_result = Mock()
        mock_result.deleted_count = 0
        self.mock_collection.delete_one.return_value = mock_result

        test_id = ObjectId()
        result = self.repo.delete_book(str(test_id))

        assert result is False

    def test_count_books(self):
        """Test counting books."""
        self.mock_collection.count_documents.return_value = 42

        count = self.repo.count_books()

        assert count == 42
        self.mock_collection.count_documents.assert_called_once_with({})

    def test_count_books_with_query(self):
        """Test counting books with query."""
        query = {"processing.status": "completed"}
        self.mock_collection.count_documents.return_value = 10

        count = self.repo.count_books(query)

        assert count == 10
        self.mock_collection.count_documents.assert_called_once_with(query)


class TestJobRepository:
    """Test job repository."""

    def setup_method(self):
        """Set up test fixtures."""
        self.repo = JobRepository()
        self.mock_collection = Mock()
        self.repo._collection = self.mock_collection

        self.test_job = ProcessingJob(
            root_directories=["/test/path1", "/test/path2"],
            status=JobStatus.RUNNING
        )

    def test_save_job_new(self):
        """Test saving new job."""
        mock_result = Mock()
        mock_result.inserted_id = ObjectId()
        self.mock_collection.insert_one.return_value = mock_result

        self.test_job.id = None
        doc_id = self.repo.save_job(self.test_job)

        assert doc_id == str(mock_result.inserted_id)
        self.mock_collection.insert_one.assert_called_once()

    def test_get_job_by_id_found(self):
        """Test getting job by ID when found."""
        test_id = ObjectId()
        mock_doc = self.test_job.model_dump(by_alias=True)
        mock_doc["_id"] = test_id
        self.mock_collection.find_one.return_value = mock_doc

        job = self.repo.get_job_by_id(str(test_id))

        assert job is not None
        assert isinstance(job, ProcessingJob)
        assert job.status == JobStatus.RUNNING

    def test_get_recent_jobs(self):
        """Test getting recent jobs."""
        mock_docs = [self.test_job.model_dump(by_alias=True)]
        mock_cursor = Mock()
        mock_cursor.sort.return_value.limit.return_value = mock_docs
        self.mock_collection.find.return_value = mock_cursor

        jobs = self.repo.get_recent_jobs(5)

        assert len(jobs) == 1
        mock_cursor.sort.assert_called_once_with("created_date", -1)
        mock_cursor.sort.return_value.limit.assert_called_once_with(5)

    def test_get_active_jobs(self):
        """Test getting active jobs."""
        mock_docs = [self.test_job.model_dump(by_alias=True)]
        self.mock_collection.find.return_value = mock_docs

        jobs = self.repo.get_active_jobs()

        assert len(jobs) == 1
        expected_query = {
            "status": {"$in": ["pending", "running"]}
        }
        self.mock_collection.find.assert_called_once_with(expected_query)
