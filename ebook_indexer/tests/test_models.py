"""Tests for database models."""

import pytest
from datetime import datetime
from bson import ObjectId

from ebook_indexer.database.models import (
    BookDocument, ProcessingJob, AnomalyReport,
    FileInfo, StructureInfo, BookMetadata, ProcessingInfo, Anomaly,
    ProcessingStatus, JobStatus, AnomalyType, AnomalySeverity, ResolutionStatus,
    JobStatistics, AnomalySummary, JobTiming, ResumeInfo,
    AnomalyContext, AnomalyResolution
)


def test_file_info_model():
    """Test FileInfo model."""
    file_info = FileInfo(
        file_path="/test/book.pdf",
        directory_path="/test",
        filename="book.pdf",
        file_extension=".pdf",
        file_size=1024000,
        file_hash="abc123",
        last_modified=datetime.utcnow()
    )

    assert file_info.file_path == "/test/book.pdf"
    assert file_info.filename == "book.pdf"
    assert file_info.file_extension == ".pdf"
    assert file_info.file_size == 1024000


def test_structure_info_model():
    """Test StructureInfo model."""
    structure_info = StructureInfo(
        expected_path="/root/collection/book-dir/book.pdf",
        collection_name="my-ebooks",
        book_directory="book-title",
        nesting_level=3,
        follows_convention=True
    )

    assert structure_info.collection_name == "my-ebooks"
    assert structure_info.nesting_level == 3
    assert structure_info.follows_convention is True


def test_book_metadata_model():
    """Test BookMetadata model."""
    metadata = BookMetadata(
        title="Test Book",
        author="Test Author",
        isbn="978-1234567890",
        publisher="Test Publisher",
        publication_date=datetime(2023, 1, 1),
        language="en",
        pages=250,
        description="A test book"
    )

    assert metadata.title == "Test Book"
    assert metadata.author == "Test Author"
    assert metadata.isbn == "978-1234567890"
    assert metadata.pages == 250


def test_processing_info_model():
    """Test ProcessingInfo model."""
    processing_info = ProcessingInfo(
        status=ProcessingStatus.COMPLETED,
        error_message=None,
        retry_count=0,
        last_processed_date=datetime.utcnow(),
        processing_time_ms=150,
        job_id=str(ObjectId())
    )

    assert processing_info.status == ProcessingStatus.COMPLETED
    assert processing_info.retry_count == 0
    assert processing_info.processing_time_ms == 150


def test_anomaly_model():
    """Test Anomaly model."""
    anomaly = Anomaly(
        type=AnomalyType.MISPLACED_FILE,
        severity=AnomalySeverity.MEDIUM,
        description="File found at wrong directory level",
        resolved=False,
        suggested_action="move_to_correct_location"
    )

    assert anomaly.type == AnomalyType.MISPLACED_FILE
    assert anomaly.severity == AnomalySeverity.MEDIUM
    assert anomaly.resolved is False
    assert anomaly.suggested_action == "move_to_correct_location"


def test_book_document_model():
    """Test complete BookDocument model."""
    file_info = FileInfo(
        file_path="/test/book.pdf",
        directory_path="/test",
        filename="book.pdf",
        file_extension=".pdf",
        file_size=1024000,
        file_hash="abc123",
        last_modified=datetime.utcnow()
    )

    structure_info = StructureInfo(
        expected_path="/root/collection/book-dir/book.pdf",
        collection_name="my-ebooks",
        book_directory="book-title",
        nesting_level=3,
        follows_convention=True
    )

    metadata = BookMetadata(
        title="Test Book",
        author="Test Author"
    )

    processing_info = ProcessingInfo(
        status=ProcessingStatus.COMPLETED,
        job_id=str(ObjectId())
    )

    anomaly = Anomaly(
        type=AnomalyType.MISPLACED_FILE,
        severity=AnomalySeverity.MEDIUM,
        description="Test anomaly"
    )

    book = BookDocument(
        file_info=file_info,
        metadata=metadata,
        structure_info=structure_info,
        processing=processing_info,
        anomalies=[anomaly]
    )

    assert book.file_info.filename == "book.pdf"
    assert book.metadata.title == "Test Book"
    assert book.structure_info.collection_name == "my-ebooks"
    assert book.processing.status == ProcessingStatus.COMPLETED
    assert len(book.anomalies) == 1
    assert book.anomalies[0].type == AnomalyType.MISPLACED_FILE


def test_processing_job_model():
    """Test ProcessingJob model."""
    job = ProcessingJob(
        root_directories=["/test/path1", "/test/path2"],
        status=JobStatus.RUNNING
    )

    assert job.root_directories == ["/test/path1", "/test/path2"]
    assert job.status == JobStatus.RUNNING
    assert isinstance(job.statistics, JobStatistics)
    assert isinstance(job.anomaly_summary, AnomalySummary)
    assert isinstance(job.timing, JobTiming)
    assert isinstance(job.resume_info, ResumeInfo)


def test_anomaly_report_model():
    """Test AnomalyReport model."""
    job_id = str(ObjectId())

    context = AnomalyContext(
        expected_location="/expected/path",
        actual_location="/actual/path",
        collection_name="test-collection"
    )

    resolution = AnomalyResolution(
        status=ResolutionStatus.UNRESOLVED,
        notes="Needs manual review"
    )

    report = AnomalyReport(
        job_id=job_id,
        file_path="/test/file.pdf",
        anomaly_type=AnomalyType.MISPLACED_FILE,
        severity=AnomalySeverity.HIGH,
        description="File in wrong location",
        context=context,
        resolution=resolution
    )

    assert report.job_id == job_id
    assert report.file_path == "/test/file.pdf"
    assert report.anomaly_type == AnomalyType.MISPLACED_FILE
    assert report.severity == AnomalySeverity.HIGH
    assert report.context.expected_location == "/expected/path"
    assert report.resolution.status == ResolutionStatus.UNRESOLVED


def test_model_serialization():
    """Test model serialization to dict."""
    file_info = FileInfo(
        file_path="/test/book.pdf",
        directory_path="/test",
        filename="book.pdf",
        file_extension=".pdf",
        file_size=1024000,
        file_hash="abc123",
        last_modified=datetime.utcnow()
    )

    # Test dict conversion
    file_dict = file_info.model_dump()
    assert file_dict["file_path"] == "/test/book.pdf"
    assert file_dict["file_size"] == 1024000

    # Test JSON serialization
    file_json = file_info.model_dump_json()
    assert "/test/book.pdf" in file_json
    assert "1024000" in file_json


def test_enum_values():
    """Test enum values."""
    assert ProcessingStatus.PENDING.value == "pending"
    assert ProcessingStatus.COMPLETED.value == "completed"
    assert ProcessingStatus.ERROR.value == "error"

    assert JobStatus.RUNNING.value == "running"
    assert JobStatus.FAILED.value == "failed"

    assert AnomalyType.MISPLACED_FILE.value == "misplaced_file"
    assert AnomalyType.DEEP_NESTING.value == "deep_nesting"

    assert AnomalySeverity.LOW.value == "low"
    assert AnomalySeverity.HIGH.value == "high"

    assert ResolutionStatus.RESOLVED.value == "resolved"
    assert ResolutionStatus.IGNORED.value == "ignored"


def test_model_validation():
    """Test model validation."""
    # Test that required fields are enforced
    with pytest.raises(ValueError):
        FileInfo()  # Missing required fields

    # Test that string validation works for job_id
    processing_info = ProcessingInfo(
        status=ProcessingStatus.COMPLETED,
        job_id="valid_string_id"
    )

    # Should work fine since we're using string for ObjectId
    result = processing_info.model_dump()
    assert result["job_id"] == "valid_string_id"
