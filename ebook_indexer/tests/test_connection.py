"""Tests for database connection."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from ebook_indexer.database.connection import (
    MongoDBConnection, init_connection, get_connection, close_connection
)
from ebook_indexer.exceptions.custom_exceptions import ConnectionError, DatabaseError


class TestMongoDBConnection:
    """Test MongoDB connection class."""

    def test_init(self):
        """Test connection initialization."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        assert conn.mongodb_url == "mongodb://localhost:27017"
        assert conn.database_name == "test_db"
        assert conn.connection_timeout == 5000
        assert conn.server_selection_timeout == 5000
        assert conn._client is None
        assert conn._database is None

    def test_init_with_custom_timeouts(self):
        """Test connection initialization with custom timeouts."""
        conn = MongoDBConnection(
            "mongodb://localhost:27017",
            "test_db",
            connection_timeout=10000,
            server_selection_timeout=15000
        )

        assert conn.connection_timeout == 10000
        assert conn.server_selection_timeout == 15000

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_connect_success(self, mock_mongo_client):
        """Test successful connection."""
        # Mock MongoDB client
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        mock_database = Mock()
        mock_client.__getitem__ = Mock(return_value=mock_database)
        mock_mongo_client.return_value = mock_client

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        database = conn.connect()

        # Verify client was created with correct parameters
        mock_mongo_client.assert_called_once_with(
            "mongodb://localhost:27017",
            connectTimeoutMS=5000,
            serverSelectionTimeoutMS=5000
        )

        # Verify ping was called
        mock_client.admin.command.assert_called_once_with('ping')

        # Verify database was set
        assert conn._client == mock_client
        assert conn._database == mock_database
        assert database == mock_database

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_connect_connection_failure(self, mock_mongo_client):
        """Test connection failure."""
        mock_mongo_client.side_effect = ConnectionFailure("Connection failed")

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        with pytest.raises(ConnectionError) as exc_info:
            conn.connect()

        assert "Failed to connect to MongoDB" in str(exc_info.value)
        assert exc_info.value.details["mongodb_url"] == "mongodb://localhost:27017"

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_connect_server_selection_timeout(self, mock_mongo_client):
        """Test server selection timeout."""
        mock_mongo_client.side_effect = ServerSelectionTimeoutError("Timeout")

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        with pytest.raises(ConnectionError) as exc_info:
            conn.connect()

        assert "Failed to connect to MongoDB" in str(exc_info.value)

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_connect_unexpected_error(self, mock_mongo_client):
        """Test unexpected connection error."""
        mock_mongo_client.side_effect = Exception("Unexpected error")

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        with pytest.raises(DatabaseError) as exc_info:
            conn.connect()

        assert "Unexpected error connecting to MongoDB" in str(exc_info.value)

    def test_disconnect(self):
        """Test disconnection."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        # Mock client
        mock_client = Mock()
        conn._client = mock_client
        conn._database = Mock()

        conn.disconnect()

        mock_client.close.assert_called_once()
        assert conn._client is None
        assert conn._database is None

    def test_disconnect_no_client(self):
        """Test disconnection when no client exists."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        # Should not raise error
        conn.disconnect()

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_get_database_when_connected(self, mock_mongo_client):
        """Test getting database when already connected."""
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        mock_database = Mock()
        mock_client.__getitem__ = Mock(return_value=mock_database)
        mock_mongo_client.return_value = mock_client

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        conn.connect()

        # Get database again
        database = conn.get_database()

        # Should return existing database without reconnecting
        assert database == mock_database
        assert mock_mongo_client.call_count == 1  # Only called once

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_get_database_when_not_connected(self, mock_mongo_client):
        """Test getting database when not connected."""
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        mock_database = Mock()
        mock_client.__getitem__ = Mock(return_value=mock_database)
        mock_mongo_client.return_value = mock_client

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        # Get database without connecting first
        database = conn.get_database()

        # Should connect automatically
        assert database == mock_database
        assert conn._client == mock_client
        assert conn._database == mock_database

    def test_get_client_when_not_connected(self):
        """Test getting client when not connected."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        with pytest.raises(ConnectionError) as exc_info:
            conn.get_client()

        assert "Not connected to MongoDB" in str(exc_info.value)

    def test_get_client_when_connected(self):
        """Test getting client when connected."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        mock_client = Mock()
        conn._client = mock_client

        client = conn.get_client()
        assert client == mock_client

    def test_is_connected_false_no_client(self):
        """Test is_connected when no client."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        assert conn.is_connected() is False

    def test_is_connected_false_ping_fails(self):
        """Test is_connected when ping fails."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        mock_client = Mock()
        mock_client.admin.command.side_effect = Exception("Ping failed")
        conn._client = mock_client

        assert conn.is_connected() is False

    def test_is_connected_true(self):
        """Test is_connected when connected."""
        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        conn._client = mock_client

        assert conn.is_connected() is True

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_health_check_success(self, mock_mongo_client):
        """Test successful health check."""
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        mock_client.server_info.return_value = {
            "version": "4.4.0",
            "gitVersion": "abc123",
            "uptime": 12345
        }
        mock_database = Mock()
        mock_client.__getitem__ = Mock(return_value=mock_database)
        mock_mongo_client.return_value = mock_client

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")
        health = conn.health_check()

        assert health["connected"] is True
        assert health["database_name"] == "test_db"
        assert health["server_info"]["version"] == "4.4.0"
        assert health["response_time_ms"] is not None
        assert health["error"] is None

    def test_health_check_failure(self):
        """Test health check failure."""
        conn = MongoDBConnection("mongodb://invalid:27017", "test_db")

        with patch.object(conn, 'connect', side_effect=ConnectionError("Connection failed")):
            health = conn.health_check()

        assert health["connected"] is False
        assert health["error"] is not None
        assert "Connection failed" in health["error"]

    @patch('ebook_indexer.database.connection.MongoClient')
    def test_context_manager(self, mock_mongo_client):
        """Test context manager functionality."""
        mock_client = Mock()
        mock_client.admin.command.return_value = {"ok": 1}
        mock_database = Mock()
        mock_client.__getitem__ = Mock(return_value=mock_database)
        mock_mongo_client.return_value = mock_client

        conn = MongoDBConnection("mongodb://localhost:27017", "test_db")

        with conn as connection:
            assert connection == conn
            assert conn._client == mock_client
            assert conn._database == mock_database

        # Should disconnect after context
        mock_client.close.assert_called_once()


class TestGlobalConnection:
    """Test global connection functions."""

    def test_init_connection(self):
        """Test initializing global connection."""
        conn = init_connection("mongodb://localhost:27017", "test_db")

        assert isinstance(conn, MongoDBConnection)
        assert conn.mongodb_url == "mongodb://localhost:27017"
        assert conn.database_name == "test_db"

    def test_get_connection_success(self):
        """Test getting global connection when initialized."""
        init_connection("mongodb://localhost:27017", "test_db")
        conn = get_connection()

        assert isinstance(conn, MongoDBConnection)

    def test_get_connection_not_initialized(self):
        """Test getting global connection when not initialized."""
        close_connection()  # Ensure no connection exists

        with pytest.raises(DatabaseError) as exc_info:
            get_connection()

        assert "MongoDB connection not initialized" in str(exc_info.value)

    def test_close_connection(self):
        """Test closing global connection."""
        init_connection("mongodb://localhost:27017", "test_db")

        # Mock the disconnect method
        conn = get_connection()
        with patch.object(conn, 'disconnect') as mock_disconnect:
            close_connection()
            mock_disconnect.assert_called_once()

    def test_close_connection_when_none(self):
        """Test closing connection when none exists."""
        close_connection()  # Ensure no connection exists

        # Should not raise error
        close_connection()
