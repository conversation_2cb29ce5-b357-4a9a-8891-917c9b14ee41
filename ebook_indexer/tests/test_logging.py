"""Tests for logging configuration."""

import logging
import tempfile
import os
from pathlib import Path

from ebook_indexer.utils.logging_config import setup_logging, get_logger, LoggerMixin


def test_setup_logging_console_only():
    """Test logging setup with console output only."""
    logger = setup_logging(log_level="DEBUG")
    
    assert logger.name == "ebook_indexer"
    assert logger.level == logging.DEBUG
    assert len(logger.handlers) == 1  # Only console handler
    assert isinstance(logger.handlers[0], logging.StreamHandler)


def test_setup_logging_with_file():
    """Test logging setup with file output."""
    with tempfile.TemporaryDirectory() as temp_dir:
        log_file = os.path.join(temp_dir, "test.log")
        
        logger = setup_logging(log_level="INFO", log_file=log_file)
        
        assert logger.name == "ebook_indexer"
        assert logger.level == logging.INFO
        assert len(logger.handlers) == 2  # Console + file handlers
        
        # Test that log file is created
        assert Path(log_file).exists()
        
        # Test logging
        logger.info("Test message")
        
        # Check file content
        with open(log_file, 'r') as f:
            content = f.read()
            assert "Test message" in content


def test_get_logger():
    """Test getting logger instance."""
    logger = get_logger()
    assert logger.name == "ebook_indexer"
    
    custom_logger = get_logger("custom")
    assert custom_logger.name == "custom"


def test_logger_mixin():
    """Test LoggerMixin functionality."""
    
    class TestClass(LoggerMixin):
        def test_method(self):
            self.logger.info("Test from mixin")
            return self.logger
    
    test_obj = TestClass()
    logger = test_obj.test_method()
    
    expected_name = f"{TestClass.__module__}.{TestClass.__name__}"
    assert logger.name == expected_name


def test_logging_levels():
    """Test different logging levels."""
    with tempfile.TemporaryDirectory() as temp_dir:
        log_file = os.path.join(temp_dir, "level_test.log")
        
        # Test WARNING level
        logger = setup_logging(log_level="WARNING", log_file=log_file)
        
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        
        # Check that only WARNING and above are logged
        with open(log_file, 'r') as f:
            content = f.read()
            assert "Debug message" not in content
            assert "Info message" not in content
            assert "Warning message" in content
            assert "Error message" in content
