"""MongoDB connection management."""

import time
from typing import Optional
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import ConnectionError, DatabaseError


class MongoDBConnection(LoggerMixin):
    """MongoDB connection manager."""
    
    def __init__(self, mongodb_url: str, database_name: str, 
                 connection_timeout: int = 5000, server_selection_timeout: int = 5000):
        """
        Initialize MongoDB connection.
        
        Args:
            mongodb_url: MongoDB connection URL
            database_name: Database name
            connection_timeout: Connection timeout in milliseconds
            server_selection_timeout: Server selection timeout in milliseconds
        """
        self.mongodb_url = mongodb_url
        self.database_name = database_name
        self.connection_timeout = connection_timeout
        self.server_selection_timeout = server_selection_timeout
        
        self._client: Optional[MongoClient] = None
        self._database: Optional[Database] = None
    
    def connect(self) -> Database:
        """
        Establish connection to MongoDB.
        
        Returns:
            Database instance
            
        Raises:
            ConnectionError: If connection fails
        """
        try:
            self.logger.info(f"Connecting to MongoDB at {self.mongodb_url}")
            
            self._client = MongoClient(
                self.mongodb_url,
                connectTimeoutMS=self.connection_timeout,
                serverSelectionTimeoutMS=self.server_selection_timeout
            )
            
            # Test the connection
            self._client.admin.command('ping')
            
            self._database = self._client[self.database_name]
            
            self.logger.info(f"Successfully connected to database '{self.database_name}'")
            return self._database
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            error_msg = f"Failed to connect to MongoDB: {e}"
            self.logger.error(error_msg)
            raise ConnectionError(error_msg, details={"mongodb_url": self.mongodb_url})
        except Exception as e:
            error_msg = f"Unexpected error connecting to MongoDB: {e}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg, details={"mongodb_url": self.mongodb_url})
    
    def disconnect(self):
        """Close MongoDB connection."""
        if self._client:
            self.logger.info("Disconnecting from MongoDB")
            self._client.close()
            self._client = None
            self._database = None
    
    def get_database(self) -> Database:
        """
        Get database instance, connecting if necessary.
        
        Returns:
            Database instance
            
        Raises:
            ConnectionError: If not connected and connection fails
        """
        if self._database is None:
            return self.connect()
        return self._database
    
    def get_client(self) -> MongoClient:
        """
        Get MongoDB client instance.
        
        Returns:
            MongoClient instance
            
        Raises:
            ConnectionError: If not connected
        """
        if self._client is None:
            raise ConnectionError("Not connected to MongoDB")
        return self._client
    
    def is_connected(self) -> bool:
        """
        Check if connected to MongoDB.
        
        Returns:
            True if connected, False otherwise
        """
        if self._client is None:
            return False
        
        try:
            # Ping the server to check connection
            self._client.admin.command('ping')
            return True
        except Exception:
            return False
    
    def health_check(self) -> dict:
        """
        Perform health check on MongoDB connection.
        
        Returns:
            Health check results
        """
        result = {
            "connected": False,
            "database_name": self.database_name,
            "mongodb_url": self.mongodb_url.split('@')[-1] if '@' in self.mongodb_url else self.mongodb_url,
            "server_info": None,
            "response_time_ms": None,
            "error": None
        }
        
        try:
            start_time = time.time()
            
            if not self.is_connected():
                self.connect()
            
            # Get server info
            server_info = self._client.server_info()
            response_time = (time.time() - start_time) * 1000
            
            result.update({
                "connected": True,
                "server_info": {
                    "version": server_info.get("version"),
                    "git_version": server_info.get("gitVersion"),
                    "uptime": server_info.get("uptime")
                },
                "response_time_ms": round(response_time, 2)
            })
            
            self.logger.debug(f"MongoDB health check passed in {response_time:.2f}ms")
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.warning(f"MongoDB health check failed: {e}")
        
        return result
    
    def create_indexes(self, collection_indexes: dict):
        """
        Create indexes for collections.
        
        Args:
            collection_indexes: Dict mapping collection names to index specifications
        """
        if not self.is_connected():
            self.connect()
        
        for collection_name, indexes in collection_indexes.items():
            collection = self._database[collection_name]
            
            for index_spec in indexes:
                try:
                    if isinstance(index_spec, dict):
                        # Complex index with options
                        keys = index_spec.get("keys")
                        options = {k: v for k, v in index_spec.items() if k != "keys"}
                        collection.create_index(keys, **options)
                        self.logger.info(f"Created index {keys} on {collection_name}")
                    else:
                        # Simple index
                        collection.create_index(index_spec)
                        self.logger.info(f"Created index {index_spec} on {collection_name}")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to create index on {collection_name}: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


# Global connection instance
_connection: Optional[MongoDBConnection] = None


def get_connection() -> MongoDBConnection:
    """
    Get global MongoDB connection instance.
    
    Returns:
        MongoDBConnection instance
        
    Raises:
        DatabaseError: If connection not initialized
    """
    global _connection
    if _connection is None:
        raise DatabaseError("MongoDB connection not initialized. Call init_connection() first.")
    return _connection


def init_connection(mongodb_url: str, database_name: str, **kwargs) -> MongoDBConnection:
    """
    Initialize global MongoDB connection.
    
    Args:
        mongodb_url: MongoDB connection URL
        database_name: Database name
        **kwargs: Additional connection parameters
        
    Returns:
        MongoDBConnection instance
    """
    global _connection
    _connection = MongoDBConnection(mongodb_url, database_name, **kwargs)
    return _connection


def close_connection():
    """Close global MongoDB connection."""
    global _connection
    if _connection:
        _connection.disconnect()
        _connection = None
