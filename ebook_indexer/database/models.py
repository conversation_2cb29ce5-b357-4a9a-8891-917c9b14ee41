"""Pydantic models for MongoDB documents."""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, ConfigDict
from bson import ObjectId


# For now, use string for ObjectId fields to avoid Pydantic v2 complexity
# We'll convert to/from ObjectId in the repository layer
PyObjectId = str


class ProcessingStatus(str, Enum):
    """Processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"
    ANOMALY = "anomaly"


class JobStatus(str, Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnomalyType(str, Enum):
    """Anomaly type enumeration."""
    MISPLACED_FILE = "misplaced_file"
    DEEP_NESTING = "deep_nesting"
    MISSING_DIRECTORY = "missing_directory"
    NAMING_CONVENTION = "naming_convention"
    DUPLICATE_FILE = "duplicate_file"


class AnomalySeverity(str, Enum):
    """Anomaly severity enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ResolutionStatus(str, Enum):
    """Anomaly resolution status."""
    UNRESOLVED = "unresolved"
    RESOLVED = "resolved"
    IGNORED = "ignored"


# File and Structure Information Models

class FileInfo(BaseModel):
    """File information model."""
    file_path: str
    directory_path: str
    filename: str
    file_extension: str
    file_size: int
    file_hash: str
    last_modified: datetime

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class StructureInfo(BaseModel):
    """File structure information model."""
    expected_path: str
    collection_name: str
    book_directory: str
    nesting_level: int
    follows_convention: bool


class BookMetadata(BaseModel):
    """Book metadata model."""
    title: Optional[str] = None
    author: Optional[str] = None
    isbn: Optional[str] = None
    publisher: Optional[str] = None
    publication_date: Optional[datetime] = None
    language: Optional[str] = None
    pages: Optional[int] = None
    description: Optional[str] = None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class ProcessingInfo(BaseModel):
    """Processing information model."""
    status: ProcessingStatus
    error_message: Optional[str] = None
    retry_count: int = 0
    last_processed_date: Optional[datetime] = None
    processing_time_ms: Optional[int] = None
    job_id: Optional[PyObjectId] = None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None,
            ObjectId: str
        }
    )


class Anomaly(BaseModel):
    """Anomaly model."""
    type: AnomalyType
    severity: AnomalySeverity
    description: str
    detected_date: datetime = Field(default_factory=datetime.utcnow)
    resolved: bool = False
    suggested_action: Optional[str] = None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


# Main Document Models

class BookDocument(BaseModel):
    """Main book document model."""
    id: Optional[PyObjectId] = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    file_info: FileInfo
    metadata: BookMetadata
    structure_info: StructureInfo
    processing: ProcessingInfo
    anomalies: List[Anomaly] = Field(default_factory=list)
    created_date: datetime = Field(default_factory=datetime.utcnow)
    updated_date: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )

    @field_validator('updated_date', mode='before')
    @classmethod
    def set_updated_date(cls, v):
        return datetime.utcnow()


class JobStatistics(BaseModel):
    """Job statistics model."""
    total_files: int = 0
    processed_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    anomalous_files: int = 0
    collections_found: int = 0
    processing_rate_files_per_sec: float = 0.0


class AnomalySummary(BaseModel):
    """Anomaly summary model."""
    total_anomalies: int = 0
    by_type: Dict[str, int] = Field(default_factory=dict)
    by_severity: Dict[str, int] = Field(default_factory=dict)


class JobTiming(BaseModel):
    """Job timing information model."""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    estimated_completion: Optional[datetime] = None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class ResumeInfo(BaseModel):
    """Job resume information model."""
    last_processed_path: Optional[str] = None
    checkpoint_data: Dict[str, Any] = Field(default_factory=dict)


class ProcessingJob(BaseModel):
    """Processing job document model."""
    id: Optional[PyObjectId] = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    root_directories: List[str]
    status: JobStatus
    statistics: JobStatistics = Field(default_factory=JobStatistics)
    anomaly_summary: AnomalySummary = Field(default_factory=AnomalySummary)
    timing: JobTiming = Field(default_factory=JobTiming)
    resume_info: ResumeInfo = Field(default_factory=ResumeInfo)
    created_date: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )


class AnomalyContext(BaseModel):
    """Anomaly context information model."""
    expected_location: Optional[str] = None
    actual_location: Optional[str] = None
    collection_name: Optional[str] = None
    suggested_book_directory: Optional[str] = None


class AnomalyResolution(BaseModel):
    """Anomaly resolution model."""
    status: ResolutionStatus = ResolutionStatus.UNRESOLVED
    action_taken: Optional[str] = None
    resolved_date: Optional[datetime] = None
    notes: str = ""

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class AnomalyReport(BaseModel):
    """Detailed anomaly report document model."""
    id: Optional[PyObjectId] = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    job_id: PyObjectId
    file_path: str
    anomaly_type: AnomalyType
    severity: AnomalySeverity
    description: str
    context: AnomalyContext = Field(default_factory=AnomalyContext)
    resolution: AnomalyResolution = Field(default_factory=AnomalyResolution)
    detected_date: datetime = Field(default_factory=datetime.utcnow)
    updated_date: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )

    @field_validator('updated_date', mode='before')
    @classmethod
    def set_updated_date(cls, v):
        return datetime.utcnow()
