"""File handling utilities."""

import os
import hashlib
import magic
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..utils.logging_config import LoggerMixin
from ..exceptions.custom_exceptions import FileProcessingError


class FileUtils(LoggerMixin):
    """Utility class for file operations."""
    
    # Supported ebook extensions
    SUPPORTED_EXTENSIONS = {'.pdf', '.epub', '.mobi', '.azw', '.azw3', '.fb2', '.txt'}
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'sha256', chunk_size: int = 8192) -> str:
        """
        Calculate file hash using specified algorithm.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm ('sha256', 'md5', 'sha1')
            chunk_size: Size of chunks to read (default 8KB)
            
        Returns:
            Hexadecimal hash string
            
        Raises:
            FileProcessingError: If file cannot be read or hashed
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                while chunk := f.read(chunk_size):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except FileNotFoundError:
            raise FileProcessingError(f"File not found: {file_path}", file_path=file_path)
        except PermissionError:
            raise FileProcessingError(f"Permission denied: {file_path}", file_path=file_path)
        except Exception as e:
            raise FileProcessingError(f"Error calculating hash for {file_path}: {e}", file_path=file_path)
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        Get file size in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File size in bytes
            
        Raises:
            FileProcessingError: If file cannot be accessed
        """
        try:
            return os.path.getsize(file_path)
        except FileNotFoundError:
            raise FileProcessingError(f"File not found: {file_path}", file_path=file_path)
        except Exception as e:
            raise FileProcessingError(f"Error getting file size for {file_path}: {e}", file_path=file_path)
    
    @staticmethod
    def get_file_modified_time(file_path: str) -> datetime:
        """
        Get file last modified time.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Last modified datetime
            
        Raises:
            FileProcessingError: If file cannot be accessed
        """
        try:
            timestamp = os.path.getmtime(file_path)
            return datetime.fromtimestamp(timestamp)
        except FileNotFoundError:
            raise FileProcessingError(f"File not found: {file_path}", file_path=file_path)
        except Exception as e:
            raise FileProcessingError(f"Error getting modified time for {file_path}: {e}", file_path=file_path)
    
    @staticmethod
    def detect_file_type(file_path: str) -> str:
        """
        Detect file MIME type using python-magic.
        
        Args:
            file_path: Path to the file
            
        Returns:
            MIME type string
            
        Raises:
            FileProcessingError: If file type cannot be detected
        """
        try:
            mime = magic.Magic(mime=True)
            return mime.from_file(file_path)
        except Exception as e:
            # Fallback to extension-based detection
            ext = Path(file_path).suffix.lower()
            mime_map = {
                '.pdf': 'application/pdf',
                '.epub': 'application/epub+zip',
                '.mobi': 'application/x-mobipocket-ebook',
                '.azw': 'application/vnd.amazon.ebook',
                '.azw3': 'application/vnd.amazon.ebook',
                '.fb2': 'application/x-fictionbook+xml',
                '.txt': 'text/plain'
            }
            return mime_map.get(ext, 'application/octet-stream')
    
    @staticmethod
    def is_supported_ebook(file_path: str) -> bool:
        """
        Check if file is a supported ebook format.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if supported ebook format
        """
        ext = Path(file_path).suffix.lower()
        return ext in FileUtils.SUPPORTED_EXTENSIONS
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Get comprehensive file information.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information
            
        Raises:
            FileProcessingError: If file cannot be processed
        """
        path_obj = Path(file_path)
        
        if not path_obj.exists():
            raise FileProcessingError(f"File does not exist: {file_path}", file_path=file_path)
        
        if not path_obj.is_file():
            raise FileProcessingError(f"Path is not a file: {file_path}", file_path=file_path)
        
        try:
            return {
                'file_path': str(path_obj.absolute()),
                'directory_path': str(path_obj.parent.absolute()),
                'filename': path_obj.name,
                'file_extension': path_obj.suffix.lower(),
                'file_size': FileUtils.get_file_size(file_path),
                'file_hash': FileUtils.calculate_file_hash(file_path),
                'last_modified': FileUtils.get_file_modified_time(file_path),
                'mime_type': FileUtils.detect_file_type(file_path),
                'is_supported': FileUtils.is_supported_ebook(file_path)
            }
        except Exception as e:
            if isinstance(e, FileProcessingError):
                raise
            raise FileProcessingError(f"Error processing file {file_path}: {e}", file_path=file_path)
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string (e.g., "1.5 MB")
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """
        Create a safe filename by removing/replacing problematic characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Safe filename
        """
        # Characters to remove or replace
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Remove leading/trailing dots and spaces
        safe_name = safe_name.strip('. ')
        
        # Ensure it's not empty
        if not safe_name:
            safe_name = "unnamed_file"
        
        return safe_name
    
    @staticmethod
    def find_files_by_extension(directory: str, extensions: List[str], recursive: bool = True) -> List[str]:
        """
        Find all files with specified extensions in directory.
        
        Args:
            directory: Directory to search
            extensions: List of extensions to find (e.g., ['.pdf', '.epub'])
            recursive: Whether to search recursively
            
        Returns:
            List of file paths
            
        Raises:
            FileProcessingError: If directory cannot be accessed
        """
        try:
            directory_path = Path(directory)
            
            if not directory_path.exists():
                raise FileProcessingError(f"Directory does not exist: {directory}")
            
            if not directory_path.is_dir():
                raise FileProcessingError(f"Path is not a directory: {directory}")
            
            files = []
            extensions_lower = [ext.lower() for ext in extensions]
            
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory_path.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in extensions_lower:
                    files.append(str(file_path.absolute()))
            
            return sorted(files)
            
        except Exception as e:
            if isinstance(e, FileProcessingError):
                raise
            raise FileProcessingError(f"Error finding files in {directory}: {e}")
    
    @staticmethod
    def validate_path(path: str) -> bool:
        """
        Validate if path exists and is accessible.
        
        Args:
            path: Path to validate
            
        Returns:
            True if path is valid and accessible
        """
        try:
            path_obj = Path(path)
            return path_obj.exists() and os.access(path, os.R_OK)
        except Exception:
            return False
