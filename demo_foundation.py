#!/usr/bin/env python3
"""Demo script to show the foundation components working."""

import sys
import tempfile
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from ebook_indexer.config.settings import AppConfig, get_config
from ebook_indexer.utils.logging_config import setup_logging
from ebook_indexer.exceptions.custom_exceptions import ConfigurationError


def demo_configuration():
    """Demonstrate configuration management."""
    print("=== Configuration Demo ===")
    
    # Default configuration
    config = AppConfig()
    print(f"Default MongoDB URL: {config.mongodb_url}")
    print(f"Default supported extensions: {config.supported_extensions}")
    print(f"Default anomaly detection settings: {config.anomaly_detection}")
    
    # Create a temporary config file
    config_content = """
mongodb:
  url: "mongodb://demo:27017"
  database: "demo_db"

root_directories:
  - "/demo/path1"
  - "/demo/path2"

processing:
  max_workers: 8
  batch_size: 50

logging:
  level: "DEBUG"
  file: "demo.log"
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(config_content)
        f.flush()
        
        try:
            # Load from file
            file_config = get_config(f.name)
            print(f"\nFrom file - MongoDB URL: {file_config.mongodb_url}")
            print(f"From file - Root directories: {file_config.root_directories}")
            print(f"From file - Max workers: {file_config.max_workers}")
            
        finally:
            os.unlink(f.name)
    
    print("✓ Configuration management working!")


def demo_logging():
    """Demonstrate logging configuration."""
    print("\n=== Logging Demo ===")
    
    # Setup logging
    logger = setup_logging(log_level="INFO", log_file="demo.log")
    
    # Test different log levels
    logger.debug("This debug message won't appear (level is INFO)")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    print("✓ Logging configuration working!")
    print("Check demo.log file for logged messages")


def demo_exceptions():
    """Demonstrate custom exceptions."""
    print("\n=== Exception Demo ===")
    
    try:
        raise ConfigurationError("Demo configuration error", details={"config_file": "missing.yaml"})
    except ConfigurationError as e:
        print(f"Caught ConfigurationError: {e.message}")
        print(f"Error details: {e.details}")
    
    print("✓ Custom exceptions working!")


def main():
    """Run all demos."""
    print("Ebook Indexer Foundation Demo")
    print("=" * 40)
    
    try:
        demo_configuration()
        demo_logging()
        demo_exceptions()
        
        print("\n" + "=" * 40)
        print("✅ All foundation components working correctly!")
        print("\nNext steps:")
        print("- Phase 2: Data Models & Database Foundation")
        print("- Phase 3: Core Business Logic")
        print("- Phase 4: Anomaly Detection")
        print("- Phase 5: Orchestration & Job Management")
        print("- Phase 6: User Interface")
        
    except Exception as e:
        print(f"\n❌ Error in demo: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
