#!/usr/bin/env python3
"""Demo script to show database components working."""

import sys
from datetime import datetime
from pathlib import Path
from bson import ObjectId

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from ebook_indexer.database.models import (
    BookDocument, ProcessingJob, AnomalyReport,
    FileInfo, StructureInfo, BookMetadata, ProcessingInfo, Anomaly,
    ProcessingStatus, JobStatus, AnomalyType, AnomalySeverity,
    AnomalyContext, AnomalyResolution, ResolutionStatus
)
from ebook_indexer.database.connection import MongoDBConnection
from ebook_indexer.utils.logging_config import setup_logging


def demo_models():
    """Demonstrate Pydantic models."""
    print("=== Database Models Demo ===")
    
    # Create file info
    file_info = FileInfo(
        file_path="/demo/ebooks/collection1/book-title/book.pdf",
        directory_path="/demo/ebooks/collection1/book-title",
        filename="book.pdf",
        file_extension=".pdf",
        file_size=2048000,
        file_hash="sha256:abc123def456",
        last_modified=datetime.utcnow()
    )
    print(f"✓ Created FileInfo: {file_info.filename} ({file_info.file_size} bytes)")
    
    # Create structure info
    structure_info = StructureInfo(
        expected_path="/demo/ebooks/collection1/book-title/book.pdf",
        collection_name="collection1",
        book_directory="book-title",
        nesting_level=3,
        follows_convention=True
    )
    print(f"✓ Created StructureInfo: {structure_info.collection_name} (level {structure_info.nesting_level})")
    
    # Create metadata
    metadata = BookMetadata(
        title="The Great Demo Book",
        author="Demo Author",
        isbn="978-0123456789",
        publisher="Demo Publishing",
        publication_date=datetime(2023, 1, 15),
        language="en",
        pages=350,
        description="A comprehensive guide to demonstrating ebook indexing."
    )
    print(f"✓ Created BookMetadata: '{metadata.title}' by {metadata.author}")
    
    # Create processing info
    job_id = ObjectId()
    processing_info = ProcessingInfo(
        status=ProcessingStatus.COMPLETED,
        error_message=None,
        retry_count=0,
        last_processed_date=datetime.utcnow(),
        processing_time_ms=250,
        job_id=job_id
    )
    print(f"✓ Created ProcessingInfo: {processing_info.status.value} in {processing_info.processing_time_ms}ms")
    
    # Create anomaly
    anomaly = Anomaly(
        type=AnomalyType.MISPLACED_FILE,
        severity=AnomalySeverity.MEDIUM,
        description="File found at correct location but naming convention could be improved",
        resolved=False,
        suggested_action="Consider renaming to follow standard convention"
    )
    print(f"✓ Created Anomaly: {anomaly.type.value} ({anomaly.severity.value})")
    
    # Create complete book document
    book = BookDocument(
        file_info=file_info,
        metadata=metadata,
        structure_info=structure_info,
        processing=processing_info,
        anomalies=[anomaly]
    )
    print(f"✓ Created BookDocument with ID: {book.id}")
    
    # Create processing job
    job = ProcessingJob(
        root_directories=["/demo/ebooks"],
        status=JobStatus.COMPLETED
    )
    job.statistics.total_files = 150
    job.statistics.processed_files = 150
    job.statistics.successful_files = 145
    job.statistics.failed_files = 3
    job.statistics.anomalous_files = 2
    job.statistics.collections_found = 5
    job.statistics.processing_rate_files_per_sec = 2.5
    
    job.anomaly_summary.total_anomalies = 8
    job.anomaly_summary.by_type = {
        "misplaced_file": 3,
        "naming_convention": 4,
        "deep_nesting": 1
    }
    job.anomaly_summary.by_severity = {
        "low": 5,
        "medium": 2,
        "high": 1
    }
    
    print(f"✓ Created ProcessingJob: {job.statistics.total_files} files processed")
    
    # Create anomaly report
    anomaly_report = AnomalyReport(
        job_id=job_id,
        file_path="/demo/ebooks/misplaced.pdf",
        anomaly_type=AnomalyType.MISPLACED_FILE,
        severity=AnomalySeverity.HIGH,
        description="PDF file found in collection root instead of book directory",
        context=AnomalyContext(
            expected_location="/demo/ebooks/collection1/book-name/book.pdf",
            actual_location="/demo/ebooks/collection1/book.pdf",
            collection_name="collection1",
            suggested_book_directory="extracted-from-title"
        ),
        resolution=AnomalyResolution(
            status=ResolutionStatus.UNRESOLVED,
            notes="Requires manual review to determine correct book directory"
        )
    )
    print(f"✓ Created AnomalyReport: {anomaly_report.anomaly_type.value}")
    
    # Test serialization
    book_dict = book.dict(by_alias=True)
    print(f"✓ Serialized BookDocument to dict ({len(book_dict)} fields)")
    
    book_json = book.json()
    print(f"✓ Serialized BookDocument to JSON ({len(book_json)} characters)")
    
    print("✓ Database models working correctly!")
    return book, job, anomaly_report


def demo_connection():
    """Demonstrate MongoDB connection (without actually connecting)."""
    print("\n=== Database Connection Demo ===")
    
    # Create connection instance
    conn = MongoDBConnection(
        mongodb_url="mongodb://localhost:27017",
        database_name="ebook_indexer_demo"
    )
    print(f"✓ Created MongoDBConnection to {conn.database_name}")
    
    # Test connection properties
    print(f"✓ Connection timeout: {conn.connection_timeout}ms")
    print(f"✓ Server selection timeout: {conn.server_selection_timeout}ms")
    
    # Test health check (will fail without MongoDB, but shows structure)
    health = conn.health_check()
    print(f"✓ Health check structure: {list(health.keys())}")
    print(f"  - Connected: {health['connected']}")
    print(f"  - Database: {health['database_name']}")
    if health['error']:
        print(f"  - Error (expected): {health['error'][:50]}...")
    
    print("✓ Database connection interface working!")
    return conn


def demo_model_validation():
    """Demonstrate model validation."""
    print("\n=== Model Validation Demo ===")
    
    # Test enum validation
    try:
        status = ProcessingStatus.COMPLETED
        print(f"✓ Valid ProcessingStatus: {status.value}")
    except Exception as e:
        print(f"✗ ProcessingStatus validation failed: {e}")
    
    # Test datetime handling
    try:
        file_info = FileInfo(
            file_path="/test/book.pdf",
            directory_path="/test",
            filename="book.pdf",
            file_extension=".pdf",
            file_size=1024,
            file_hash="test123",
            last_modified=datetime.utcnow()
        )
        print(f"✓ DateTime validation working: {file_info.last_modified}")
    except Exception as e:
        print(f"✗ DateTime validation failed: {e}")
    
    # Test ObjectId handling
    try:
        test_id = ObjectId()
        processing_info = ProcessingInfo(
            status=ProcessingStatus.PENDING,
            job_id=test_id
        )
        print(f"✓ ObjectId validation working: {processing_info.job_id}")
    except Exception as e:
        print(f"✗ ObjectId validation failed: {e}")
    
    # Test required field validation
    try:
        # This should work
        minimal_file_info = FileInfo(
            file_path="/test.pdf",
            directory_path="/",
            filename="test.pdf",
            file_extension=".pdf",
            file_size=100,
            file_hash="hash",
            last_modified=datetime.utcnow()
        )
        print("✓ Required field validation working")
    except Exception as e:
        print(f"✗ Required field validation failed: {e}")
    
    print("✓ Model validation working correctly!")


def main():
    """Run all database demos."""
    print("Ebook Indexer Database Demo")
    print("=" * 40)
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    try:
        # Demo models
        book, job, anomaly_report = demo_models()
        
        # Demo connection
        conn = demo_connection()
        
        # Demo validation
        demo_model_validation()
        
        print("\n" + "=" * 40)
        print("✅ All database components working correctly!")
        print("\nDatabase Features Demonstrated:")
        print("- ✅ Pydantic models with validation")
        print("- ✅ MongoDB document schemas")
        print("- ✅ Enum types for status and categories")
        print("- ✅ DateTime and ObjectId handling")
        print("- ✅ Model serialization (dict/JSON)")
        print("- ✅ Connection management interface")
        print("- ✅ Health check functionality")
        print("\nNext: Connect to actual MongoDB and test repositories!")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error in database demo: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
