#!/usr/bin/env python3
"""Simple test runner for development."""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run all tests."""
    project_root = Path(__file__).parent
    
    # Add project root to Python path
    sys.path.insert(0, str(project_root))
    
    # Run pytest
    cmd = [sys.executable, "-m", "pytest", "ebook_indexer/tests/", "-v"]
    
    print("Running tests...")
    print(f"Command: {' '.join(cmd)}")
    
    result = subprocess.run(cmd, cwd=project_root)
    return result.returncode


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)
