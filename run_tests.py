#!/usr/bin/env python3
"""Simple test runner for development."""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run all tests using uv."""
    project_root = Path(__file__).parent

    # Run pytest with uv
    cmd = ["uv", "run", "pytest", "ebook_indexer/tests/", "-v"]

    print("Running tests with uv...")
    print(f"Command: {' '.join(cmd)}")

    result = subprocess.run(cmd, cwd=project_root)
    return result.returncode


def install_deps():
    """Install dependencies using uv."""
    project_root = Path(__file__).parent

    # Install dependencies including dev dependencies
    cmd = ["uv", "sync", "--extra", "dev"]

    print("Installing dependencies with uv...")
    print(f"Command: {' '.join(cmd)}")

    result = subprocess.run(cmd, cwd=project_root)
    return result.returncode


if __name__ == "__main__":
    print("Installing dependencies...")
    install_code = install_deps()
    if install_code != 0:
        print("Failed to install dependencies")
        sys.exit(install_code)

    print("\nRunning tests...")
    exit_code = run_tests()
    sys.exit(exit_code)
